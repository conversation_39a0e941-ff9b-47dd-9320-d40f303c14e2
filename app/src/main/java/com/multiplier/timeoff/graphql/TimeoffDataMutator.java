package com.multiplier.timeoff.graphql;

import com.multiplier.common.transport.auth.MPLAuthorization;
import com.multiplier.common.transport.user.CurrentUser;
import com.multiplier.timeoff.DgsConstants;
import com.multiplier.timeoff.adapters.ApprovalServiceAdapter;
import com.multiplier.timeoff.kafka.TimeoffKafkaPublisher;
import com.multiplier.timeoff.processor.AllocationProcessor;
import com.multiplier.timeoff.processor.CarryForwardExpirationProcessor;
import com.multiplier.timeoff.processor.CarryForwardProcessor;
import com.multiplier.timeoff.processor.FixAllocationProcessor;
import com.multiplier.timeoff.service.DefinitionService;
import com.multiplier.timeoff.service.TimeoffService;
import com.multiplier.timeoff.service.TimeoffSummaryService;
import com.multiplier.timeoff.service.TimeoffTypeService;
import com.multiplier.timeoff.types.*;
import com.netflix.graphql.dgs.DgsComponent;
import com.netflix.graphql.dgs.DgsDataFetchingEnvironment;
import com.netflix.graphql.dgs.DgsMutation;
import com.netflix.graphql.dgs.InputArgument;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@DgsComponent
@RequiredArgsConstructor
public class TimeoffDataMutator {

    private final TimeoffService timeoffService;
    private final CurrentUser currentUser;
    private final ApprovalServiceAdapter approvalServiceAdapter;
    private final AllocationProcessor allocationProcessor;
    private final CarryForwardProcessor carryForwardProcessor;
    private final CarryForwardExpirationProcessor carryForwardExpirationProcessor;
    private final TimeoffKafkaPublisher timeoffKafkaPublisher;
    private final FixAllocationProcessor fixAllocationProcessor;
    private final MPLAuthorization mplAuthorization;
    private final TimeoffTypeService timeoffTypeService;
    private final DefinitionService definitionService;
    private final TimeoffSummaryService timeoffSummaryService;
    private static final String SUCCESS_MESSAGE = "Success with debugging infos (if any) = ";

    @PreAuthorize("@me.allowed('create.member.timeoffs') || @me.allowed('create.company.time-off')")
    @DgsMutation(field = DgsConstants.MUTATION.TimeOffCreate)
    public TimeOff submit(@InputArgument Long contractId, @InputArgument TimeOffCreateInput input, DgsDataFetchingEnvironment dfe) {
        TimeOff timeOff = timeoffService.submit(dfe, contractId, input);
        try {
            approvalServiceAdapter.startApproval(timeOff.getId());
            log.info("Approval process started for timeOff; id =" + timeOff.getId());
            if (shouldAutoApproveTimeoff(timeOff)) {
                approvalServiceAdapter.autoApproveItem(timeOff.getId());
                timeOff.setStatus(TimeOffStatus.APPROVED); // just to reflect correct status to FE
                timeoffKafkaPublisher.publishTimeoffUpdateEvent(timeOff);
                log.info("Auto Approval process started for timeOff; id =" + timeOff.getId());
            }
        } catch (Exception ex) {
            log.warn("Exception occurred in start Approval for timeOffID: {}", timeOff.getId(), ex);
        }
        return timeOff;
    }

    @PreAuthorize("@me.allowed('create.member.timeoffs') || @me.allowed('create.company.time-off')")
    @DgsMutation(field = DgsConstants.MUTATION.TimeOffCreateV2)
    public TimeOff submitV2(@InputArgument TimeOffCreateInputV2 input,
        DgsDataFetchingEnvironment dfe) {
        log.info("[submitV2] request received with input : {}", input);
        TimeOff timeOff = timeoffService.submitV2(dfe, input);
        try {
            approvalServiceAdapter.startApproval(timeOff.getId());
            log.info("Approval process started for timeOff; id =" + timeOff.getId());
            if (shouldAutoApproveTimeoff(timeOff)) {
                approvalServiceAdapter.autoApproveItem(timeOff.getId());
                timeOff.setStatus(TimeOffStatus.APPROVED); // just to reflect correct status to FE
                timeoffKafkaPublisher.publishTimeoffUpdateEvent(timeOff);
                log.info("Auto Approval process started for timeOff; id =" + timeOff.getId());
            }
        } catch (Exception ex) {
            log.warn("Exception occurred in start Approval for timeOffID: {}", timeOff.getId(), ex);
        }
        return timeOff;
    }

    @PreAuthorize("@me.allowed('create.member.timeoffs') || @me.allowed('create.company.time-off')")
    @DgsMutation(field = DgsConstants.MUTATION.TimeOffSaveAsDraft)
    public TimeOff saveAsDraft(@InputArgument TimeOffSaveAsDraftInput input, DgsDataFetchingEnvironment dfe) {
        return timeoffService.saveAsDraft(input, dfe);
    }

    @PreAuthorize("@me.allowed('create.member.timeoffs') || @me.allowed('create.company.time-off')")
    @DgsMutation(field = DgsConstants.MUTATION.TimeOffSaveAsDraftV2)
    public TimeOff saveAsDraftV2(@InputArgument TimeOffSaveAsDraftInputV2 input, DgsDataFetchingEnvironment dfe) {
        log.info("[saveAsDraftV2] request received with input : {}", input);
        return timeoffService.saveAsDraftV2(input, dfe);
    }

    /**
     * To set a timeoff status back to DRAFT<br>
     */
    @DgsMutation(field = DgsConstants.MUTATION.TimeOffRevoke)
    public TimeOff revoke(@InputArgument Long id, DgsDataFetchingEnvironment dfe) {
        return timeoffService.revoke(dfe, id);
    }

    @DgsMutation(field = DgsConstants.MUTATION.TimeOffBulkRevoke)
    public TaskResponse timeOffBulkRevoke(@InputArgument List<Long> ids) {
        return timeoffService.bulkRevokeTimeOffs(ids);
    }

    @PreAuthorize("@me.allowed('update.member.timeoffs') || @me.allowed('update.company.time-off')")
    @DgsMutation(field = DgsConstants.MUTATION.TimeOffUpdate)
    public TimeOff update(@InputArgument Long id, @InputArgument TimeOffUpdateInput input, DgsDataFetchingEnvironment dfe) {

        return timeoffService.update(id, input, dfe);
    }


    @DgsMutation(field = DgsConstants.MUTATION.TimeOffDelete)
    public TaskResponse delete(@InputArgument Long id, DgsDataFetchingEnvironment dfe) {

        timeoffService.delete(id, dfe);
        return new TaskResponse(Boolean.TRUE, "Successfully deleted Time-off.");
    }


    @DgsMutation(field = DgsConstants.MUTATION.TriggerLegacyTimeoffReallocation)
    public TaskResponse triggerLegacyTimeoffReallocation() {
        try {
            log.info("[triggerLegacyTimeoffReallocation] Received request to re-allocate timeoff summaries");
            timeoffService.reallocateTimeoff(LocalDate.now());
            return new TaskResponse(Boolean.TRUE, "Timeoff balance reallocation job started.");
        }
        catch (Exception ex) {
            log.error("Legacy timeoff reallocation failed", ex);
            return new TaskResponse(Boolean.FALSE, "Timeoff balance reallocation failed: " + ex.getMessage());
        }
    }

    @DgsMutation(field = DgsConstants.MUTATION.TriggerTimeoffAllocation)
    public TaskResponse triggerTimeoffAllocation(@InputArgument TimeOffAllocationInput input) {
        try {
            allocationProcessor.allocateTimeoffBalances(input);
            return new TaskResponse(Boolean.TRUE, "Timeoff balance allocated successfully.");
        }
        catch (Exception ex) {
            log.error("Timeoff balance allocation failed", ex);
            return new TaskResponse(Boolean.FALSE, "Timeoff balance allocation failed: " + ex.getMessage());
        }
    }

    @DgsMutation(field = DgsConstants.MUTATION.UpdateTimeOffSummary)
    public TaskResponse updateTimeOffSummary(@InputArgument List<UpdateTimeOffSummaryInput> input) {
        log.info("[updateTimeOffSummary] Received request: {}", input);

        try {
            timeoffSummaryService.updateTimeOffSummary(input);
            return TaskResponse.newBuilder()
                    .success(true)
                    .message("Timeoff summaries are successfully updated.")
                    .build();
        } catch (Exception e) {
            log.error("[updateTimeOffSummary] Timeoff summary update failed.", e);
            return TaskResponse.newBuilder()
                    .success(false)
                    .message("Summary update process failed: " + e.getMessage())
                    .build();
        }
    }


    @DgsMutation(field = DgsConstants.MUTATION.TriggerTimeoffCarryForward)
    public TaskResponse triggerTimeoffCarryForward(@InputArgument TimeOffAllocationInput input) {
        try {
            carryForwardProcessor.carryForwardTimeoffBalances(input);
            return new TaskResponse(Boolean.TRUE, "Timeoff balance carried successfully.");
        }
        catch (Exception ex) {
            log.error("Timeoff balance carry forward failed", ex);
            return new TaskResponse(Boolean.FALSE, "Timeoff balance carry forward failed: " + ex.getMessage());
        }
    }

    @DgsMutation(field = DgsConstants.MUTATION.BackfillTimeoffAllocationRecords)
    public TaskResponse backfillTimeoffAllocationRecords(@InputArgument TimeOffAllocationInput input) {
        try {
            allocationProcessor.backfillAllocationRecords(input);
            return new TaskResponse(Boolean.TRUE, "Timeoff allocation record backfilled successfully.");
        }
        catch (Exception ex) {
            log.error("Timeoff allocation record backfill failed", ex);
            return new TaskResponse(Boolean.FALSE, "Timeoff allocation record backfill failed: " + ex.getMessage());
        }
    }

    @DgsMutation(field = DgsConstants.MUTATION.BackfillTimeoffCarryForwardRecords)
    public TaskResponse backfillTimeoffCarryForwardRecords(@InputArgument TimeOffAllocationInput input) {
        try {
            carryForwardProcessor.backfillCarryForwardRecords(input);
            return new TaskResponse(Boolean.TRUE, "Timeoff carry forward record backfilled successfully.");
        }
        catch (Exception ex) {
            log.error("Timeoff carry forward record backfill failed", ex);
            return new TaskResponse(Boolean.FALSE, "Timeoff carry forward record backfill failed: " + ex.getMessage());
        }
    }

    @DgsMutation(field = DgsConstants.MUTATION.TriggerCarryForwardExpiration)
    public TaskResponse triggerCarryForwardExpiration(@InputArgument TimeOffAllocationInput input) {
        try {
            carryForwardExpirationProcessor.expireCarryForwardTimeoffBalances(input);
            return new TaskResponse(Boolean.TRUE, "Carry forward expiration triggered successfully.");
        }
        catch (Exception ex) {
            log.error("Carry forward expiration failed", ex);
            return new TaskResponse(Boolean.FALSE, "Carry forward expiration failed: " + ex.getMessage());
        }
    }

    private boolean shouldAutoApproveTimeoff(TimeOff timeOff) {
        String experience = currentUser.getContext().getExperience();
        return "company".equals(experience) && timeOff.getStatus() == TimeOffStatus.APPROVAL_IN_PROGRESS;
    }

    @DgsMutation(field = DgsConstants.MUTATION.MigrateFromBackToWorkDateToEndDate)
    public TaskResponse migrateFromBackToWorkDateToEndDate() {
        return TaskResponse.newBuilder()
                .success(false)
                .message("This mutation is deprecated. Please contact squad phoenix for more details")
                .build();
    }

    @DgsMutation(field = DgsConstants.MUTATION.FixTimeoffAllocations)
    public TaskResponse fixTimeoffAllocations(@InputArgument FixTimeoffAllocationInput input) {
        try {
            fixAllocationProcessor.fixTimeoffAllocations(input.getTimeoffSummaryIds(), input.getDryRun());
            return new TaskResponse(Boolean.TRUE, "Fix timeoff allocation triggered successfully.");
        }
        catch (Exception ex) {
            log.error("Fix timeoff allocation failed", ex);
            return new TaskResponse(Boolean.FALSE, "Fix timeoff allocation failed: " + ex.getMessage());
        }
    }

    @DgsMutation(field = DgsConstants.MUTATION.BackfillTimeoffApprovedOn)
    public TaskResponse backfillTimeoffApprovedOn(@InputArgument List<Long> ids) {
        try {
            log.info("Backfilling timeoffs approvedOn");
            timeoffService.backfillTimeoffApprovedOn(ids);
            log.info("Backfilling timeoffs approvedOn successful");
            return new TaskResponse(Boolean.TRUE, "Timeoff.approved_on backfilled successfully.");
        } catch (Exception ex) {
            log.error("Timeoff.approved_on backfill failed", ex);
            return new TaskResponse(Boolean.FALSE, "Timeoff.approved_on backfill failed: " + ex.getMessage());
        }
    }

    @DgsMutation(field = DgsConstants.MUTATION.FixEntitlementChangeRecords)
    public TaskResponse fixEntitlementChangeRecords(@InputArgument List<Long> contractIds) {
        log.info("[fixEntitlementChangeRecords] with contractIds: {}", contractIds);
        try {
            List<String> debuggingInfos = timeoffService.fixEntitlementChangeRecords(sanitize(contractIds));
            log.info("[fixEntitlementChangeRecords] debuggingInfos={}", debuggingInfos);
            return TaskResponse.newBuilder().success(true).message(SUCCESS_MESSAGE + debuggingInfos).build();
        } catch (Exception e) {
            log.warn("[fixEntitlementChangeRecords] Exception occurred: ", e);
            return TaskResponse.newBuilder()
                    .success(false)
                    .message("An exception occurred. Kindly contact @Thang for more details: " + e)
                    .build();
        }
    }


    /**
     * For Ops Admin
     *
     * @param id                 not null
     * @param startDate          not null
     * @param endDate            not null
     * @param ignoresValidations nullable, default false
     */
    @DgsMutation(field = DgsConstants.MUTATION.ChangeTimeOffDate)
    public TaskResponse changeTimeOffDate(@InputArgument long id, @InputArgument LocalDate startDate, @InputArgument LocalDate endDate, @InputArgument Boolean ignoresValidations) {
        log.info("[changeTimeOffDate] with id: {}, startDate: {}, endDate: {}, ignoresValidations: {}", id, startDate, endDate, ignoresValidations);
        try {
            List<String> debuggingInfos = timeoffService.changeTimeOffDate(id, startDate, endDate, Boolean.TRUE.equals(ignoresValidations));
            log.info("[changeTimeOffDate] debuggingInfos={}", debuggingInfos);
            return TaskResponse.newBuilder().success(true).message(SUCCESS_MESSAGE + debuggingInfos).build();
        } catch (Exception e) {
            log.warn("[changeTimeOffDate] Exception occurred: ", e);
            return TaskResponse.newBuilder()
                    .success(false)
                    .message("An exception occurred. Kindly contact @Thang for more details: " + e)
                    .build();
        }
    }

    @DgsMutation(field = DgsConstants.MUTATION.ChangeTimeOffDateV2)
    public TaskResponse changeTimeOffDateV2(@InputArgument TimeOffChangeDateInput input) {
        log.info("[changeTimeOffDateV2] Received request: {}", input);

        try {
            List<String> debuggingInfos = timeoffService.changeTimeOffDateV2(input);
            log.info("[changeTimeOffDateV2] debuggingInfos={}", debuggingInfos);

            return TaskResponse.newBuilder()
                    .success(true)
                    .message(SUCCESS_MESSAGE + debuggingInfos)
                    .build();

        } catch (Exception e) {
            log.warn("[changeTimeOffDateV2] Exception occurred: ", e);
            return TaskResponse.newBuilder()
                    .success(false)
                    .message("An exception occurred. Kindly contact @squad-phoenix for more details: " + e)
                    .build();
        }
    }

    @DgsMutation(field = DgsConstants.MUTATION.TimeOffTypeCreate)
    public TimeOffTypeInfo timeOffTypeCreate(@InputArgument TimeOffTypeCreateInput input) {
        log.info("[timeOffTypeCreate] with input : {}", input);
        return timeoffTypeService.createTimeOffType(input);
    }

    @DgsMutation(field = DgsConstants.MUTATION.TimeOffTypeUpdate)
    public TimeOffTypeInfo timeOffTypeUpdate(@InputArgument TimeOffTypeUpdateInput input) {
        log.info("[timeOffTypeUpdate] with input : {}", input);
        return timeoffTypeService.updateTimeOffType(input);
    }

    @DgsMutation(field = DgsConstants.MUTATION.TimeOffTypeDelete)
    public TimeOffTypeInfo timeOffTypeDelete(@InputArgument Long id) {
        log.info("[timeOffTypeDelete] with id : {}", id);
        return timeoffTypeService.deleteTimeOffType(id);
    }

    @DgsMutation(field = DgsConstants.MUTATION.TimeOffPolicyCreate)
    public TimeOffTypeDefinition timeOffPolicyCreate(@InputArgument TimeOffPolicyCreateInput input) {
        log.info("[timeOffPolicyCreate] with input : {}", input);
        return definitionService.createCompanyDefinition(input);
    }

    @DgsMutation(field = DgsConstants.MUTATION.TimeOffPolicyDelete)
    public TimeOffTypeDefinition timeOffPolicyDelete(@InputArgument Long policyId) {
        log.info("[timeOffPolicyDelete] with policyId : {}", policyId);
        return definitionService.deleteDefinition(policyId);
    }

    @DgsMutation(field = DgsConstants.MUTATION.TimeOffPolicyUpdate)
    public TimeOffTypeDefinition timeOffPolicyUpdate(@InputArgument TimeOffPolicyUpdateInput input) {
        log.info("[timeOffPolicyUpdate] with input : {}", input);
        return definitionService.updateDefinition(input);
    }

    @DgsMutation(field = DgsConstants.MUTATION.TimeOffPolicyValidateUsers)
    public TimeOffPolicyUsersValidationResult timeOffPolicyUsersValidate(@InputArgument TimeOffPolicyValidateUsersInput input, DgsDataFetchingEnvironment dfe) {
        log.info("[timeOffPolicyUsersValidate] with input : {}", input);
        return definitionService.validateDefinitionAssignment(input, dfe);
    }

    @DgsMutation(field = DgsConstants.MUTATION.TimeOffPolicyAssignUsers)
    public TimeOffTypeDefinition timeOffPolicyAssignUsers(@InputArgument TimeOffPolicyAssignUsersInput input, DgsDataFetchingEnvironment dfe) {
        log.info("[timeOffPolicyAssignUsers] with input : {}", input);
        return definitionService.assignOrUnassignUsers(dfe, input);
    }

    @DgsMutation(field = DgsConstants.MUTATION.BackfillEntitlementDefinitionIds)
    public TaskResponse backFillDefinitionIdInEntitlement(@InputArgument Long companyId) {
        timeoffService.backFillDefinitionIdsForEntitlements(companyId);
        return TaskResponse.newBuilder()
                .success(true)
                .message("Back-fill process started successfully")
                .build();
    }

    @DgsMutation(field = DgsConstants.MUTATION.BackfillRulesForExistingCompanyDefinitions)
    public TaskResponse backfillRulesForExistingCompanyDefinitions(@InputArgument Long companyId, @InputArgument boolean forAll) {
        return TaskResponse.newBuilder()
                .success(false)
                .message("This mutation is deprecated. Please contact squad phoenix for more details")
                .build();
    }

    @DgsMutation(field = DgsConstants.MUTATION.MigrateCompanyPoliciesToEntityPolicies)
    public TaskResponse migrateCompanyDefinitions(@InputArgument List<Long> companyIds, @InputArgument boolean forAll) {
        try {
            definitionService.migrateCompanyLevelDefinitionsToEntityLevel(companyIds, forAll);
            return TaskResponse.newBuilder()
                    .success(true)
                    .message("Migrating company definitions, process started successfully")
                    .build();
        } catch (Exception e) {
            log.error("Error while migrating company definitions to entity level", e);
            return TaskResponse.newBuilder()
                    .success(false)
                    .message("Error while migrating company definitions to entity level: " + e.getMessage())
                    .build();
        }
    }

    private Set<Long> sanitize(Collection<Long> longs) {
        if (longs == null) {
            return Set.of();
        }
        return longs.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
    }
}
